#!/usr/bin/env python3
"""
Modern Transaction Executor with QuickNode Bundles and Advanced Error Handling
🔧 UPGRADED: Now uses QuickNode Bundles for better reliability and performance.
Resolves signature verification failures through modern Solana transaction practices.
"""

import asyncio
import logging
import time
import base64
import base58
from typing import Dict, Any, Optional, List
import httpx

logger = logging.getLogger(__name__)

class ModernTransactionExecutor:
    """🔧 UPGRADED: Modern transaction executor with QuickNode Bundles and premium RPC handling."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize with unified configuration."""
        # Load unified configuration if not provided
        if config is None:
            from phase_4_deployment.core.unified_config import get_unified_config
            unified_config = get_unified_config()
            unified_config.load()

            self.rpc_config = unified_config.get_rpc_config()
            self.execution_config = unified_config.get_execution_config()
            self.quicknode_config = unified_config.get_quicknode_config()
        else:
            # Use provided config (for backward compatibility)
            self.rpc_config = {
                'primary_rpc': config.get('primary_rpc', 'https://mainnet.helius-rpc.com'),
                'fallback_rpc': config.get('fallback_rpc', 'https://api.mainnet-beta.solana.com'),
                'jito_rpc': config.get('jito_rpc', 'https://mainnet.block-engine.jito.wtf/api/v1'),
                'quicknode_bundle_url': config.get('quicknode_bundle_url', 'https://api.quicknode.com/v1/solana/mainnet/bundles'),
                'helius_api_key': config.get('helius_api_key'),
                'quicknode_api_key': config.get('quicknode_api_key'),
                'timeout': config.get('timeout', 30.0),
                'max_retries': config.get('max_retries', 3)
            }
            self.execution_config = {
                'circuit_breaker_enabled': config.get('circuit_breaker_enabled', True),
                'failure_threshold': config.get('failure_threshold', 3),
                'reset_timeout': config.get('reset_timeout', 60)
            }
            self.quicknode_config = {
                'enabled': config.get('quicknode_bundles_enabled', True),
                'api_url': config.get('quicknode_bundle_url', 'https://api.quicknode.com/v1/solana/mainnet/bundles'),
                'api_key': config.get('quicknode_api_key'),
                'fallback_to_jito': config.get('quicknode_fallback_jito', True)
            }

        # RPC Configuration
        self.primary_rpc = self.rpc_config['primary_rpc']
        self.fallback_rpc = self.rpc_config['fallback_rpc']
        self.jito_rpc = self.rpc_config['jito_rpc']
        self.quicknode_bundle_url = self.rpc_config.get('quicknode_bundle_url', 'https://api.quicknode.com/v1/solana/mainnet/bundles')

        # API Keys
        self.helius_api_key = self.rpc_config['helius_api_key']
        self.quicknode_api_key = self.rpc_config['quicknode_api_key']

        # HTTP Clients
        self.primary_client = None
        self.fallback_client = None
        self.jito_client = None
        self.quicknode_client = None  # 🔧 NEW: QuickNode bundle client

        # Circuit Breaker State (using configuration values)
        self.circuit_breaker = {
            'primary': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'},
            'fallback': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'},
            'jito': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'},
            'quicknode': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'}  # 🔧 NEW: QuickNode circuit breaker
        }

        # Circuit breaker configuration
        self.failure_threshold = self.execution_config['failure_threshold']
        self.reset_timeout = self.execution_config['reset_timeout']
        self.circuit_breaker_enabled = self.execution_config['circuit_breaker_enabled']

        # Metrics
        self.metrics = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'signature_verification_failures': 0,
            'jito_bundle_successes': 0,
            'quicknode_bundle_successes': 0,  # 🔧 NEW: QuickNode bundle metrics
            'average_execution_time': 0.0
        }

    async def initialize(self):
        """Initialize HTTP clients with proper configuration."""
        try:
            # Primary RPC client (QuickNode with API key)
            self.primary_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=20, max_keepalive_connections=10)
            )

            # Fallback RPC client (Helius)
            self.fallback_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )

            # Jito client for bundles (fallback)
            self.jito_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )

            # 🔧 NEW: QuickNode bundle client (primary)
            if self.quicknode_config.get('enabled', True):
                from phase_4_deployment.rpc_execution.jito_bundle_client import QuickNodeBundleClient
                self.quicknode_client = QuickNodeBundleClient(
                    api_url=self.quicknode_bundle_url,
                    api_key=self.quicknode_api_key,
                    rpc_url=self.primary_rpc
                )
                logger.info("✅ QuickNode bundle client initialized")

            logger.info("✅ Modern transaction executor initialized with QuickNode bundles")

        except Exception as e:
            logger.error(f"❌ Error initializing transaction executor: {e}")
            raise

    async def execute_transaction_with_bundles(self,
                                             transaction: str,
                                             opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """🔧 UPGRADED: Execute transaction using QuickNode Bundles (primary) or Jito Bundles (fallback)."""
        start_time = time.time()
        self.metrics['total_transactions'] += 1

        try:
            # Decode transaction
            if isinstance(transaction, str):
                try:
                    # Try base64 first
                    tx_bytes = base64.b64decode(transaction)
                except:
                    # Fallback to base58
                    tx_bytes = base58.b58decode(transaction)
            else:
                tx_bytes = transaction

            # FIXED: Jupiter transactions must be submitted immediately
            # Jupiter API provides pre-signed transactions with fresh blockhash
            # These transactions expire within 1-2 seconds and must be submitted immediately
            logger.info("⚡ FIXED: Using Jupiter pre-signed transaction for immediate submission")
            logger.info("⚡ Transaction has ~1-2 second window before blockhash expires")

            # Use original transaction bytes directly - submit immediately
            updated_tx_bytes = tx_bytes

            # FIX 4: Add Transaction Simulation for pre-validation
            simulation_result = await self._simulate_transaction(updated_tx_bytes)
            if not simulation_result.get('success'):
                logger.error(f"❌ Transaction simulation failed: {simulation_result.get('error')}")
                # Still proceed but log the warning
                logger.warning("⚠️ Proceeding with transaction despite simulation failure")

            # FIX 3: Optimize Transaction Timing - Parallel execution with timeouts
            tasks = []

            # 🔧 PRIORITY 1: Try QuickNode Bundle execution first (with timeout)
            if self.quicknode_client and not self._is_circuit_open('quicknode'):
                logger.info("🔧 Attempting QuickNode bundle execution (primary)")
                quicknode_task = asyncio.create_task(
                    asyncio.wait_for(
                        self._execute_quicknode_bundle([updated_tx_bytes]),
                        timeout=1.0  # FIX 3: Limit QuickNode to 1.0s
                    )
                )
                tasks.append(('quicknode', quicknode_task))

            # FIX 3: Start Jito in parallel if QuickNode is available but give it a head start
            if not self._is_circuit_open('jito') and len(tasks) > 0:
                # Give QuickNode a 0.3s head start, then try Jito in parallel
                try:
                    await asyncio.wait_for(tasks[0][1], timeout=0.3)
                    # QuickNode succeeded quickly
                    result = tasks[0][1].result()
                    if result.get('success'):
                        execution_time = time.time() - start_time
                        logger.info(f"✅ QuickNode bundle executed in {execution_time:.2f}s")
                        self._update_metrics(True, execution_time)
                        self.metrics['quicknode_bundle_successes'] += 1
                        return result
                except asyncio.TimeoutError:
                    # QuickNode is slow, start Jito in parallel
                    logger.info("🔧 Starting Jito bundle in parallel (QuickNode slow)")
                    jito_task = asyncio.create_task(
                        asyncio.wait_for(
                            self._execute_jito_bundle([updated_tx_bytes]),
                            timeout=0.7  # FIX 3: Limit Jito to 0.7s
                        )
                    )
                    tasks.append(('jito', jito_task))

            # FIX 3: Wait for first successful result or all to complete
            if tasks:
                # Extract just the task objects for asyncio.wait
                task_objects = [task for provider, task in tasks]
                done, pending = await asyncio.wait(task_objects, return_when=asyncio.FIRST_COMPLETED)

                # Check completed tasks for success
                for task in done:
                    # Find the provider for this task
                    provider = next(p for p, t in tasks if t == task)
                    try:
                        result = task.result()
                        if result.get('success'):
                            execution_time = time.time() - start_time
                            logger.info(f"✅ {provider.title()} bundle executed in {execution_time:.2f}s")
                            self._update_metrics(True, execution_time)
                            self.metrics[f'{provider}_bundle_successes'] += 1

                            # Cancel pending tasks
                            for pending_task in pending:
                                pending_task.cancel()

                            return result
                        else:
                            self._record_failure(provider)
                            logger.warning(f"⚠️ {provider.title()} bundle failed: {result.get('error')}")
                    except asyncio.CancelledError:
                        logger.info(f"⏹️ {provider.title()} bundle task was cancelled")
                        self._record_failure(provider)
                    except Exception as e:
                        self._record_failure(provider)
                        logger.error(f"❌ {provider.title()} bundle error: {e}")

                # Cancel any remaining tasks
                for pending_task in pending:
                    pending_task.cancel()

            # Check timing before fallback
            execution_time = time.time() - start_time
            if execution_time > 1.5:
                logger.warning(f"⚠️ TIMING WARNING: Bundle attempts took {execution_time:.2f}s (>1.5s may cause failures)")

            logger.warning("⚠️ All bundles failed, using regular transaction")

            # 🔧 PRIORITY 3: Fallback to regular transaction execution
            return await self._execute_regular_transaction(updated_tx_bytes, opts)

        except Exception as e:
            logger.error(f"❌ Error executing transaction: {e}")
            execution_time = time.time() - start_time
            self._update_metrics(False, execution_time)

            return {
                'success': False,
                'error': f"Transaction execution failed: {str(e)}",
                'provider': None,
                'signature': None
            }

    async def _execute_quicknode_bundle(self, transactions: List[bytes]) -> Dict[str, Any]:
        """🔧 NEW: Execute transactions as a QuickNode Bundle."""
        try:
            if not self.quicknode_client:
                logger.error("❌ QuickNode client not initialized")
                return {'success': False, 'error': 'QuickNode client not available'}

            # Encode transactions for bundle
            encoded_txs = [base64.b64encode(tx).decode('utf-8') for tx in transactions]

            logger.info(f"🔧 Submitting QuickNode bundle with {len(encoded_txs)} transactions")

            # Submit bundle using QuickNode client
            result = await self.quicknode_client.submit_bundle(
                transactions=encoded_txs,
                priority_fee=20000  # 0.00002 SOL priority fee
            )

            if result.get('success'):
                bundle_id = result.get('bundle_id')
                logger.info(f"✅ QuickNode bundle submitted successfully: {bundle_id}")

                return {
                    'success': True,
                    'bundle_id': bundle_id,
                    'status': result.get('status'),
                    'provider': 'quicknode_bundle',
                    'signature': bundle_id,  # Use bundle_id as signature for tracking
                    'execution_type': 'quicknode_bundle'
                }
            else:
                error_msg = result.get('error', 'Unknown QuickNode error')
                logger.error(f"❌ QuickNode bundle failed: {error_msg}")
                return {'success': False, 'error': error_msg}

        except Exception as e:
            logger.error(f"❌ Error executing QuickNode bundle: {e}")
            return {'success': False, 'error': str(e)}

    async def _execute_jito_bundle(self, transactions: List[bytes]) -> Dict[str, Any]:
        """Execute transactions as a Jito Bundle."""
        try:
            if not self.jito_client:
                await self.initialize()

            # Encode transactions for bundle
            encoded_txs = [base64.b64encode(tx).decode('utf-8') for tx in transactions]

            # Prepare bundle request
            bundle_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendBundle",
                "params": [encoded_txs]
            }

            # Send bundle to Jito
            response = await self.jito_client.post(
                f"{self.jito_rpc}/bundles",
                json=bundle_request,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    bundle_id = result["result"]
                    logger.info(f"✅ Jito bundle submitted successfully: {bundle_id}")

                    # Wait for bundle confirmation
                    confirmation = await self._wait_for_bundle_confirmation(bundle_id)

                    return {
                        'success': True,
                        'bundle_id': bundle_id,
                        'confirmation': confirmation,
                        'provider': 'jito_bundle',
                        'signature': confirmation.get('signature') if confirmation else None
                    }
                else:
                    error_msg = result.get('error', {}).get('message', 'Unknown error')
                    logger.error(f"❌ Jito bundle failed: {error_msg}")
                    return {'success': False, 'error': error_msg}
            else:
                logger.error(f"❌ Jito bundle HTTP error: {response.status_code}")
                return {'success': False, 'error': f"HTTP {response.status_code}"}

        except Exception as e:
            logger.error(f"❌ Error executing Jito bundle: {e}")
            return {'success': False, 'error': str(e)}

    async def _execute_regular_transaction(self, tx_bytes: bytes, opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute transaction using regular RPC with retry logic."""
        try:
            # Encode transaction
            encoded_tx = base64.b64encode(tx_bytes).decode('utf-8')

            # Prepare transaction request with immediate submission optimization
            tx_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    encoded_tx,
                    {
                        "encoding": "base64",
                        "skipPreflight": True,  # FIXED: Skip preflight for immediate submission
                        "preflightCommitment": "processed",  # FIXED: Use processed for speed
                        "maxRetries": 0  # We handle retries ourselves
                    }
                ]
            }

            # Try primary RPC first
            if not self._is_circuit_open('primary'):
                result = await self._send_transaction_request(self.primary_client, self.primary_rpc, tx_request)
                if result.get('success'):
                    return result
                else:
                    self._record_failure('primary')
                    if 'signature verification failure' in str(result.get('error', '')).lower():
                        self.metrics['signature_verification_failures'] += 1

            # Fallback to secondary RPC
            if not self._is_circuit_open('fallback'):
                result = await self._send_transaction_request(self.fallback_client, self.fallback_rpc, tx_request)
                if result.get('success'):
                    return result
                else:
                    self._record_failure('fallback')

            return {'success': False, 'error': 'All RPC endpoints failed'}

        except Exception as e:
            logger.error(f"❌ Error in regular transaction execution: {e}")
            return {'success': False, 'error': str(e)}

    async def _send_transaction_request(self, client: httpx.AsyncClient, rpc_url: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send transaction request to RPC endpoint."""
        try:
            response = await client.post(rpc_url, json=request)
            response.raise_for_status()

            result = response.json()

            if "result" in result:
                signature = result["result"]

                # Check for placeholder signature that indicates transaction failure
                if signature == "1111111111111111111111111111111111111111111111111111111111111111":
                    logger.error("❌ Received placeholder signature - transaction failed on blockchain")
                    return {
                        'success': False,
                        'error': 'Transaction failed - received placeholder signature from RPC',
                        'signature': signature,
                        'provider': rpc_url
                    }

                logger.info(f"✅ Transaction sent successfully: {signature}")
                return {
                    'success': True,
                    'signature': signature,
                    'provider': rpc_url
                }
            elif "error" in result:
                error_msg = result["error"].get("message", "Unknown error")
                logger.error(f"❌ RPC error: {error_msg}")
                return {'success': False, 'error': error_msg}
            else:
                return {'success': False, 'error': 'Invalid RPC response'}

        except Exception as e:
            logger.error(f"❌ Error sending transaction request: {e}")
            return {'success': False, 'error': str(e)}

    async def _simulate_transaction(self, tx_bytes: bytes) -> Dict[str, Any]:
        """FIX 4: Simulate transaction before submission for pre-validation."""
        try:
            # Encode transaction for simulation
            encoded_tx = base64.b64encode(tx_bytes).decode('utf-8')

            simulation_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "simulateTransaction",
                "params": [
                    encoded_tx,
                    {
                        "encoding": "base64",
                        "commitment": "processed",
                        "sigVerify": False,  # Skip signature verification for speed
                        "replaceRecentBlockhash": True  # Use latest blockhash
                    }
                ]
            }

            # Try primary RPC first for simulation
            if not self._is_circuit_open('primary'):
                try:
                    response = await asyncio.wait_for(
                        self.primary_client.post(self.primary_rpc, json=simulation_request),
                        timeout=2.0  # Quick timeout for simulation
                    )
                    response.raise_for_status()
                    result = response.json()

                    if "result" in result:
                        sim_result = result["result"]
                        if sim_result.get("value", {}).get("err") is None:
                            logger.info("✅ Transaction simulation successful")
                            return {
                                'success': True,
                                'logs': sim_result.get("value", {}).get("logs", []),
                                'compute_units': sim_result.get("value", {}).get("unitsConsumed", 0)
                            }
                        else:
                            error = sim_result.get("value", {}).get("err")
                            logger.warning(f"⚠️ Transaction simulation failed: {error}")
                            return {'success': False, 'error': f"Simulation error: {error}"}
                    else:
                        error_msg = result.get('error', {}).get('message', 'Unknown simulation error')
                        return {'success': False, 'error': error_msg}
                except asyncio.TimeoutError:
                    logger.warning("⚠️ Transaction simulation timed out")
                    return {'success': False, 'error': 'Simulation timeout'}
                except Exception as e:
                    logger.warning(f"⚠️ Simulation error on primary RPC: {e}")

            # Quick fallback simulation
            if not self._is_circuit_open('fallback'):
                try:
                    response = await asyncio.wait_for(
                        self.fallback_client.post(self.fallback_rpc, json=simulation_request),
                        timeout=1.0  # Even quicker timeout for fallback
                    )
                    response.raise_for_status()
                    result = response.json()

                    if "result" in result:
                        sim_result = result["result"]
                        if sim_result.get("value", {}).get("err") is None:
                            logger.info("✅ Transaction simulation successful (fallback)")
                            return {'success': True}
                        else:
                            error = sim_result.get("value", {}).get("err")
                            return {'success': False, 'error': f"Simulation error: {error}"}
                except Exception as e:
                    logger.warning(f"⚠️ Simulation error on fallback RPC: {e}")

            return {'success': False, 'error': 'All simulation endpoints failed'}

        except Exception as e:
            logger.warning(f"⚠️ Transaction simulation error: {e}")
            return {'success': False, 'error': str(e)}

    async def _get_fresh_blockhash(self) -> Optional[str]:
        """Get fresh blockhash from the most reliable RPC."""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "finalized"}]
            }

            # Try primary RPC first
            if not self._is_circuit_open('primary'):
                try:
                    response = await self.primary_client.post(self.primary_rpc, json=request)
                    response.raise_for_status()
                    result = response.json()

                    if "result" in result and "value" in result["result"]:
                        return result["result"]["value"]["blockhash"]
                except:
                    pass

            # Fallback RPC
            if not self._is_circuit_open('fallback'):
                try:
                    response = await self.fallback_client.post(self.fallback_rpc, json=request)
                    response.raise_for_status()
                    result = response.json()

                    if "result" in result and "value" in result["result"]:
                        return result["result"]["value"]["blockhash"]
                except:
                    pass

            return None

        except Exception as e:
            logger.error(f"❌ Error getting fresh blockhash: {e}")
            return None

    async def _wait_for_bundle_confirmation(self, bundle_id: str, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """Wait for Jito bundle confirmation."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Check bundle status
                status_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBundleStatuses",
                    "params": [[bundle_id]]
                }

                response = await self.jito_client.post(
                    f"{self.jito_rpc}/bundles",
                    json=status_request
                )

                if response.status_code == 200:
                    result = response.json()
                    if "result" in result and result["result"]:
                        status = result["result"][0]
                        if status.get("confirmation_status") == "confirmed":
                            return status

                await asyncio.sleep(1.0)  # Wait 1 second before retry

            except Exception as e:
                logger.warning(f"⚠️ Error checking bundle status: {e}")
                await asyncio.sleep(1.0)

        logger.warning(f"⚠️ Bundle confirmation timeout: {bundle_id}")
        return None

    def _is_circuit_open(self, provider: str) -> bool:
        """Check if circuit breaker is open for a provider."""
        circuit = self.circuit_breaker.get(provider, {})
        if circuit.get('state') == 'OPEN':
            # Check if reset timeout has passed
            if time.time() - circuit.get('last_failure', 0) > 60:  # 60 second reset
                circuit['state'] = 'CLOSED'
                circuit['failures'] = 0
                return False
            return True
        return False

    def _record_failure(self, provider: str):
        """Record failure for circuit breaker."""
        circuit = self.circuit_breaker.get(provider, {})
        circuit['failures'] = circuit.get('failures', 0) + 1
        circuit['last_failure'] = time.time()

        if circuit['failures'] >= 3:  # Open circuit after 3 failures
            circuit['state'] = 'OPEN'
            logger.warning(f"⚠️ Circuit breaker opened for {provider}")

    def _update_metrics(self, success: bool, execution_time: float):
        """Update execution metrics."""
        if success:
            self.metrics['successful_transactions'] += 1
        else:
            self.metrics['failed_transactions'] += 1

        # Update average execution time
        total_txs = self.metrics['successful_transactions'] + self.metrics['failed_transactions']
        current_avg = self.metrics['average_execution_time']
        self.metrics['average_execution_time'] = ((current_avg * (total_txs - 1)) + execution_time) / total_txs

    async def get_metrics(self) -> Dict[str, Any]:
        """Get executor metrics."""
        return {
            **self.metrics,
            'circuit_breaker_status': self.circuit_breaker
        }

    async def close(self):
        """Close all HTTP clients."""
        if self.primary_client:
            await self.primary_client.aclose()
        if self.fallback_client:
            await self.fallback_client.aclose()
        if self.jito_client:
            await self.jito_client.aclose()
        if self.quicknode_client:  # 🔧 NEW: Close QuickNode client
            await self.quicknode_client.close()

        logger.info("✅ Modern transaction executor closed")
