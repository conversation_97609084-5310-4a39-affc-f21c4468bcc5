#!/usr/bin/env python3
"""
Unified Live Trading Entry Point
Aligns all live trading components with proper transaction signing and Jupiter configuration.
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging with safe directory creation
def setup_logging():
    """Setup logging with safe directory creation."""
    try:
        # Ensure logs directory exists
        logs_dir = project_root / 'logs'
        logs_dir.mkdir(exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logs_dir / 'unified_live_trading.log'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        # Fallback to console-only logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"⚠️ Warning: Could not setup file logging: {e}")

setup_logging()
logger = logging.getLogger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle non-serializable types."""

    def default(self, obj):
        if isinstance(obj, bool):
            return obj
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        elif hasattr(obj, 'isoformat'):
            return obj.isoformat()
        else:
            return str(obj)


class UnifiedLiveTrader:
    """Unified live trading system with proper transaction signing and execution."""

    def __init__(self, modern_executor=None, jupiter_client=None, config=None):
        """Initialize the unified live trader with optional modern components.

        Args:
            modern_executor: Modern transaction executor with signature verification fix
            jupiter_client: Modern Jupiter client with optimized execution
            config: Configuration dictionary (optional)
        """
        # Load environment variables with validation
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        self.keypair_path = os.getenv('KEYPAIR_PATH')
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.birdeye_api_key = os.getenv('BIRDEYE_API_KEY')

        # Initialize wallet balance tracking
        self.wallet_balance = 0.0

        # Trading configuration
        self.dry_run = os.getenv('DRY_RUN', 'true').lower() == 'true'  # Default to dry run for safety
        self.paper_trading = os.getenv('PAPER_TRADING', 'false').lower() == 'true'
        self.trading_enabled = os.getenv('TRADING_ENABLED', 'false').lower() == 'true'

        # Modern components (injected dependencies)
        self.modern_executor = modern_executor
        self.modern_jupiter_client = jupiter_client
        self.use_modern_components = modern_executor is not None
        self.components_initialized = False

        # Components (will be set to modern or legacy in initialize_components)
        self.tx_prep_service = None
        self.executor = None
        self.tx_builder = None
        self.telegram_notifier = None

        # Validate critical environment variables
        self.validation_errors = []
        self._validate_environment()

        if self.use_modern_components:
            logger.info("🚀 UnifiedLiveTrader initialized with modern components (signature verification fix enabled)")
        else:
            logger.info("⚠️ UnifiedLiveTrader initialized with legacy components")

    def _validate_environment(self):
        """Validate environment variables and configuration."""
        if not self.wallet_address:
            self.validation_errors.append("WALLET_ADDRESS not set")

        if not self.helius_api_key:
            self.validation_errors.append("HELIUS_API_KEY not set")

        if not self.dry_run and not self.keypair_path:
            self.validation_errors.append("KEYPAIR_PATH required for live trading")

        if self.keypair_path and not os.path.exists(self.keypair_path):
            self.validation_errors.append(f"Keypair file not found: {self.keypair_path}")

        # Log validation results
        if self.validation_errors:
            logger.warning("⚠️ Environment validation issues:")
            for error in self.validation_errors:
                logger.warning(f"   - {error}")
        else:
            logger.info("✅ Environment validation passed")

    async def initialize_components(self):
        """Initialize all trading components with proper configuration."""
        # Skip re-initialization if components are already initialized
        if self.components_initialized:
            logger.info("✅ Components already initialized - skipping re-initialization")
            return True

        logger.info("🔧 Initializing trading components...")

        # Check for validation errors first
        if self.validation_errors:
            logger.error("❌ Cannot initialize components due to validation errors:")
            for error in self.validation_errors:
                logger.error(f"   - {error}")
            return False

        # FIXED: Use modern components with immediate submission (signature verification fix)
        if self.use_modern_components:
            logger.info("⚡ FIXED: Using modern components with immediate submission (signature verification fix enabled)")
            logger.info("⚡ Jupiter transactions will be submitted within 1-2 seconds to prevent blockhash expiration")
            self.executor = self.modern_executor

            # Initialize modern executor if needed
            if hasattr(self.executor, 'initialize') and not hasattr(self.executor, '_initialized'):
                await self.executor.initialize()
                self.executor._initialized = True
                logger.info("✅ Modern executor initialized with immediate submission optimization")

            # Initialize minimal required components for modern mode
            await self._initialize_minimal_components()
            self.components_initialized = True
            logger.info("⚡ FIXED: Modern components initialized with signature verification fix")
            return True

        try:
            # LIVE TRADING: Only use modern components - no legacy fallbacks
            logger.info("🚀 LIVE TRADING: Initializing modern components only")

            # Load keypair if available
            keypair = None
            if self.keypair_path and os.path.exists(self.keypair_path):
                try:
                    # Try to import solders keypair
                    try:
                        from solders.keypair import Keypair
                        logger.info("✅ Solders keypair module available")
                    except ImportError:
                        logger.warning("⚠️ Solders not available, using fallback keypair handling")
                        keypair = None
                        # Continue without keypair for now
                        logger.info("✅ Continuing without keypair (dry run mode)")

                    if 'Keypair' in locals():
                        import json

                    # Try to load as JSON array first
                    try:
                        with open(self.keypair_path, 'r') as f:
                            keypair_data = json.load(f)
                        if isinstance(keypair_data, list) and len(keypair_data) in [32, 64]:
                            # Handle both 32-byte (private key only) and 64-byte (private + public key) formats
                            if len(keypair_data) == 64:
                                # Use the full 64-byte array
                                keypair_bytes = bytes(keypair_data)
                                keypair = Keypair.from_bytes(keypair_bytes)
                            else:
                                # 32-byte private key only, extend to 64 bytes
                                keypair_bytes = bytes(keypair_data + [0] * 32)
                                keypair = Keypair.from_bytes(keypair_bytes[:32])
                        else:
                            logger.error(f"❌ Invalid keypair format: expected 32 or 64-byte array, got {len(keypair_data) if isinstance(keypair_data, list) else 'non-array'}")
                            return False
                    except json.JSONDecodeError:
                        # Try to load as raw bytes
                        with open(self.keypair_path, 'rb') as f:
                            keypair_bytes = f.read()
                        keypair = Keypair.from_bytes(keypair_bytes)

                    logger.info(f"✅ Loaded keypair from {self.keypair_path}")
                except Exception as e:
                    logger.error(f"❌ Error loading keypair: {str(e)}")
                    return False

            # Initialize Orca swap builder (replaces Jupiter)
            from core.dex.orca_swap_builder import OrcaSwapBuilder
            self.orca_builder = OrcaSwapBuilder(self.wallet_address)
            await self.orca_builder.initialize()
            logger.info("✅ Orca swap builder initialized")

            # LIVE TRADING: Initialize bundle clients for modern executor
            from phase_4_deployment.rpc_execution.jito_bundle_client import JitoBundleClient

            jito_bundle_client = JitoBundleClient(
                block_engine_url="https://ny.mainnet.block-engine.jito.wtf",
                rpc_url=f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
                max_retries=3,
                retry_delay=1.0,
                timeout=30.0
            )
            logger.info("✅ LIVE TRADING: Jito Bundle client initialized")

            # Store the bundle client for modern executor
            self.jito_bundle_client = jito_bundle_client

            # 🔧 LIVE TRADING: Use single ModernTransactionExecutor (no duplicates)
            if not self.executor:  # Only initialize if not already set
                try:
                    from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor

                    self.executor = ModernTransactionExecutor(
                        config={
                            'primary_rpc': f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}",
                            'fallback_rpc': "https://api.mainnet-beta.solana.com",
                            'jito_rpc': "https://ny.mainnet.block-engine.jito.wtf/api/v1",
                            'helius_api_key': self.helius_api_key,
                            'timeout': 30.0,
                            'max_retries': 3,
                            'circuit_breaker_enabled': True,
                            'failure_threshold': 3,
                            'reset_timeout': 60
                        }
                    )
                    await self.executor.initialize()
                    logger.info(f"✅ LIVE TRADING: Single modern executor initialized (no duplicates)")

                except ImportError:
                    logger.error("❌ LIVE TRADING: ModernTransactionExecutor required for live trading")
                    return False
            else:
                logger.info("✅ LIVE TRADING: Using existing modern executor (no duplicate initialization)")

            # Initialize Telegram notifier
            try:
                from core.notifications.telegram_notifier import TelegramNotifier
                self.telegram_notifier = TelegramNotifier()
                if self.telegram_notifier.enabled:
                    logger.info("✅ Telegram notifier initialized")
                else:
                    logger.warning("⚠️ Telegram notifier disabled (credentials not found)")
            except ImportError:
                logger.warning("⚠️ Telegram notifier not available")
                self.telegram_notifier = None

            # Mark components as initialized for legacy mode
            self.components_initialized = True
            return True

        except Exception as e:
            logger.error(f"❌ Error initializing components: {str(e)}")
            return False

    async def _initialize_minimal_components(self):
        """Initialize minimal components required for modern mode."""
        try:
            # Initialize Orca swap builder (still needed for transaction building)
            from core.dex.orca_swap_builder import OrcaSwapBuilder
            self.orca_builder = OrcaSwapBuilder(self.wallet_address)
            await self.orca_builder.initialize()
            logger.info("✅ Orca swap builder initialized (modern mode)")

            # Initialize Telegram notifier
            try:
                from core.notifications.telegram_notifier import TelegramNotifier
                self.telegram_notifier = TelegramNotifier()
                if self.telegram_notifier.enabled:
                    logger.info("✅ Telegram notifier initialized (modern mode)")
                else:
                    logger.warning("⚠️ Telegram notifier disabled (credentials not found)")
            except ImportError:
                logger.warning("⚠️ Telegram notifier not available")
                self.telegram_notifier = None

            logger.info("✅ Modern components initialization complete")

        except Exception as e:
            logger.error(f"❌ Error initializing minimal components: {str(e)}")
            raise

    async def get_current_wallet_balance(self):
        """Get current wallet balance for PnL calculations."""
        try:
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient

            client = HeliusClient(api_key=self.helius_api_key)
            balance_data = await client.get_balance(self.wallet_address)

            if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
                balance = balance_data['balance_sol']
                # Update the instance wallet balance
                self.wallet_balance = balance
                return balance
            else:
                logger.warning(f"⚠️ Could not get wallet balance: {balance_data}")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting wallet balance: {e}")
            return None

    async def check_wallet_balance(self):
        """Check wallet balance and ensure sufficient funds."""
        logger.info("💰 Checking wallet balance...")

        try:
            balance_sol = await self.get_current_wallet_balance()

            if balance_sol is not None:
                logger.info(f"✅ Wallet balance: {balance_sol:.6f} SOL")

                if balance_sol < 0.1:
                    logger.error(f"❌ Insufficient balance: {balance_sol:.6f} SOL (minimum 0.1 SOL required)")
                    return False

                return True
            else:
                logger.error("❌ Could not retrieve wallet balance")
                return False

        except Exception as e:
            logger.error(f"❌ Error checking wallet balance: {str(e)}")
            return False

    async def build_orca_transaction(self, signal):
        """Build an Orca swap transaction from a trading signal with Jupiter fallback."""
        logger.info(f"🔨 Building Orca transaction for {signal['market']}")

        try:
            # Validate signal parameters
            if not await self.orca_builder.validate_swap_parameters(signal):
                logger.error("❌ Invalid swap parameters")
                return None

            # Build Orca swap transaction
            logger.info("🌊 Building Orca swap transaction...")
            orca_result = await self.orca_builder.build_swap_transaction(signal)

            if orca_result and orca_result.get('success'):
                logger.info("✅ Orca swap transaction built successfully")
                return orca_result
            else:
                error = orca_result.get('error', 'Unknown error') if orca_result else 'No result returned'
                logger.warning(f"⚠️ Orca transaction failed: {error}")

                # Try Jupiter fallback
                logger.info("🔄 Attempting Jupiter fallback...")
                jupiter_result = await self.build_jupiter_transaction(signal)

                if jupiter_result and jupiter_result.get('success'):
                    logger.info("✅ Jupiter fallback transaction built successfully")
                    return jupiter_result
                else:
                    logger.error("❌ Both Orca and Jupiter failed to build transaction")
                    return None

        except Exception as e:
            logger.error(f"❌ Error building Orca transaction: {str(e)}")
            # Try Jupiter fallback on exception
            try:
                logger.info("🔄 Attempting Jupiter fallback after Orca exception...")
                jupiter_result = await self.build_jupiter_transaction(signal)
                if jupiter_result and jupiter_result.get('success'):
                    logger.info("✅ Jupiter fallback transaction built successfully")
                    return jupiter_result
            except Exception as jupiter_error:
                logger.error(f"❌ Jupiter fallback also failed: {jupiter_error}")

            return None

    async def build_jupiter_transaction(self, signal):
        """Build a Jupiter swap transaction as fallback when Orca fails."""
        logger.info(f"🪐 Building Jupiter transaction for {signal['market']}")

        try:
            # Import Jupiter fallback
            from phase_4_deployment.utils.jupiter_swap_fallback import get_jupiter_fallback

            jupiter_client = get_jupiter_fallback()

            # Build Jupiter swap transaction with signing
            wallet_pubkey = self.wallet_address

            # Get keypair for signing
            keypair = None
            if hasattr(self, 'keypair') and self.keypair:
                keypair = self.keypair
            elif hasattr(self.orca_builder, 'keypair') and self.orca_builder.keypair:
                keypair = self.orca_builder.keypair

            transaction_data = await jupiter_client.build_simple_swap(signal, wallet_pubkey, keypair)

            if transaction_data:
                # Return in the same format as Orca
                result = {
                    'success': True,
                    'transaction': transaction_data,  # Jupiter returns signed base64 transaction
                    'execution_type': 'jupiter_swap',
                    'signal': signal,
                    'provider': 'jupiter'
                }
                logger.info("✅ Jupiter swap transaction built and signed successfully")
                return result
            else:
                logger.error("❌ Jupiter failed to build transaction")
                return {'success': False, 'error': 'Jupiter transaction building failed'}

        except Exception as e:
            logger.error(f"❌ Error building Jupiter transaction: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def execute_trade(self, signal):
        """Execute a trade from a trading signal with wallet balance validation."""
        logger.info(f"🚀 Executing trade: {signal['action']} {signal['market']} {signal['size']}")

        try:
            # Get wallet balance BEFORE trade for validation
            balance_before = await self.get_wallet_balance()
            logger.info(f"💰 Wallet balance before trade: {balance_before} SOL")

            # Build transaction with immediate blockhash handling
            transaction = await self._build_transaction_immediate(signal)
            if not transaction:
                logger.error("❌ Failed to build transaction with immediate blockhash")
                return None

            # Execute transaction
            if not self.dry_run:
                logger.info("💸 Executing live transaction with immediate execution...")
                start_time = datetime.now()

                # Check if this is a Jito Bundle result
                if isinstance(transaction, dict) and transaction.get('execution_type') == 'jito_bundle':
                    logger.info("✅ Jito Bundle executed successfully!")
                    result = transaction  # Bundle result is already formatted
                    execution_time = (datetime.now() - start_time).total_seconds()
                else:
                    # Regular transaction execution with immediate handling
                    logger.info("🔄 Executing via immediate transaction execution")
                    result = await self._execute_transaction_immediate(transaction)
                    execution_time = (datetime.now() - start_time).total_seconds()

                if result and result.get('success', False):
                    if result.get('execution_type') == 'orca_swap':
                        logger.info(f"✅ Orca swap executed successfully: {result.get('signature', 'N/A')}")
                        logger.info(f"🌊 Input: {result.get('input_amount', 0)} {result.get('input_token', 'Unknown')[:8]}...")
                        logger.info(f"🌊 Output: {result.get('estimated_output', 0)} {result.get('output_token', 'Unknown')[:8]}...")
                        logger.info(f"📊 Slippage: {result.get('slippage_bps', 0)} bps")
                    elif result.get('execution_type') == 'jito_bundle':
                        logger.info(f"✅ Jito Bundle executed successfully: {result.get('bundle_id', 'N/A')}")
                        logger.info(f"💰 Tip amount: {result.get('tip_amount', 0)} lamports")
                        logger.info(f"🛡️ MEV Protection: Enabled (Jito Bundle)")
                    else:
                        logger.info(f"✅ Transaction executed successfully: {result.get('signature', 'N/A')}")
                    logger.info(f"⏱️ Execution time: {execution_time:.2f} seconds")

                    # CRITICAL: Validate wallet balance changed
                    await asyncio.sleep(3)  # Wait for blockchain confirmation
                    balance_after = await self.get_wallet_balance()
                    logger.info(f"💰 Wallet balance after trade: {balance_after} SOL")

                    if balance_after != balance_before:
                        logger.info(f"✅ TRADE CONFIRMED: Wallet balance changed from {balance_before} to {balance_after} SOL")
                        logger.info(f"📈 Balance change: {balance_after - balance_before:.6f} SOL")
                    else:
                        logger.warning(f"⚠️ WARNING: Wallet balance unchanged - trade may not have executed")

                    # Save trade record
                    await self.save_trade_record(signal, result, execution_time, balance_before, balance_after)

                    # Send Telegram notification
                    if self.telegram_notifier and self.telegram_notifier.enabled:
                        try:
                            # FIXED: Get actual wallet balance for PnL calculation
                            current_balance = await self.get_current_wallet_balance()
                            if current_balance is None:
                                logger.warning("⚠️ Could not get current balance for PnL calculation")
                                current_balance = 3.1  # Fallback estimate

                            trade_data = {
                                'signal': signal,
                                'position_data': {
                                    'position_size_sol': signal.get('size', 0),
                                    'position_size_usd': signal.get('size', 0) * signal.get('price', 0),
                                    'total_wallet_sol': current_balance  # FIXED: Use actual balance
                                },
                                'transaction_result': {
                                    'success': True,
                                    'signature': result.get('signature', 'N/A'),
                                    'execution_time': execution_time
                                },
                                'timestamp': datetime.now().isoformat()
                            }
                            await self.telegram_notifier.notify_trade_executed(trade_data)
                            logger.info("📱 Telegram notification sent")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to send Telegram notification: {e}")

                    return result
                else:
                    logger.error(f"❌ Transaction failed: {result.get('error', 'Unknown error')}")
                    return None
            else:
                logger.info("🧪 Dry run mode - transaction not executed")
                return {'success': True, 'signature': 'dry_run_test', 'provider': 'dry_run'}

        except Exception as e:
            logger.error(f"❌ Error executing trade: {str(e)}")
            return None

    async def save_trade_record(self, signal, result, execution_time, balance_before=None, balance_after=None):
        """Save trade record to file with balance validation."""
        try:
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'signal': signal,
                'result': result,
                'execution_time': execution_time,
                'wallet_address': self.wallet_address,
                'mode': 'live' if not self.dry_run else 'dry_run',
                'balance_validation': {
                    'balance_before': balance_before,
                    'balance_after': balance_after,
                    'balance_changed': balance_after != balance_before if balance_before and balance_after else None,
                    'balance_change': balance_after - balance_before if balance_before and balance_after else None
                }
            }

            # Save to trades directory
            trades_dir = 'output/live_production/trades'
            os.makedirs(trades_dir, exist_ok=True)

            filename = f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(trades_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(trade_record, f, indent=2, cls=CustomJSONEncoder)

            logger.info(f"💾 Trade record saved: {filepath}")

        except Exception as e:
            logger.error(f"❌ Error saving trade record: {str(e)}")

    async def run_trading_cycle(self):
        """Run a single trading cycle."""
        logger.info("🔄 Running trading cycle...")

        try:
            # Import signal generation components with error handling
            try:
                from phase_4_deployment.data_router.birdeye_scanner import BirdeyeScanner
                from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
                from core.risk.production_position_sizer import ProductionPositionSizer
                from core.strategies.market_regime_detector import MarketRegimeDetector
                from core.strategies.adaptive_weight_manager import AdaptiveWeightManager
                from core.analytics.strategy_attribution import StrategyAttributionTracker
                from core.strategies.strategy_selector import StrategySelector
                logger.info("✅ Signal generation modules imported successfully")
            except ImportError as e:
                logger.warning(f"⚠️ Signal generation modules not available: {e}")
                # Use fallback signal generation
                return await self.run_fallback_trading_cycle()

            # Initialize position sizer with current wallet balance
            current_balance = await self.get_wallet_balance()

            # 🚀 PHASE 2: Initialize Market Regime Detector for timing filters
            regime_detector = MarketRegimeDetector(
                config={'market_regime': {
                    'enabled': True,
                    'regime_confidence_threshold': 0.6,  # Require 60% confidence
                    'adx_period': 14,
                    'bb_period': 20,
                    'choppiness_period': 14
                }}
            )
            logger.info("📊 Initialized market regime detector for timing filters")

            # 🚀 PHASE 2: Initialize Signal Enricher for confidence enhancement
            signal_enricher = SignalEnricher(
                config={
                    'enabled': True,
                    'ranking_algorithm': 'composite_score',
                    'score_weights': {
                        'momentum_score': 0.4,      # High weight on momentum
                        'liquidity_score': 0.3,     # Volume/liquidity confirmation
                        'volatility_score': 0.2,    # Volatility consideration
                        'alpha_wallet_score': 0.1   # Alpha wallet activity
                    }
                }
            )
            logger.info("🎯 Initialized signal enricher for confidence-based scaling")

            # 🚀 PHASE 3: Initialize Adaptive Strategy System
            adaptive_weight_manager = AdaptiveWeightManager(
                config={'adaptive_weighting': {
                    'learning_rate': 0.02,                    # Slightly faster learning
                    'weight_update_interval': 1800,           # 30 minutes for live trading
                    'min_strategy_weight': 0.05,              # 5% minimum allocation
                    'max_strategy_weight': 0.7,               # 70% maximum allocation
                    'performance_lookback_days': 7,           # 1 week lookback
                    'regime_adjustment_factor': 0.3,          # 30% regime influence
                    'risk_adjustment_factor': 0.2             # 20% risk influence
                }}
            )
            logger.info("🎯 Initialized adaptive weight manager for strategy optimization")

            # 🚀 PHASE 3: Initialize Strategy Attribution for performance tracking
            strategy_attribution = StrategyAttributionTracker(
                config={'strategy_attribution': {
                    'attribution_window_days': 7,      # 1 week attribution window
                    'min_trades_for_attribution': 3    # Minimum trades for reliable stats
                }}
            )
            logger.info("📊 Initialized strategy attribution for performance tracking")

            # 🔧 PHASE 3: Initialize Enhanced Whale Watcher with QuickNode Streaming
            try:
                from phase_4_deployment.data_router.enhanced_whale_watcher import get_whale_watcher
                whale_watcher = await get_whale_watcher()

                # Register whale alert callback
                def whale_alert_callback(whale_alert):
                    try:
                        logger.info(f"🐋 WHALE ALERT: {whale_alert.amount_sol:.2f} SOL (${whale_alert.amount_usd:,.2f}) - {whale_alert.alert_level.upper()}")
                        # Could trigger additional trading logic here
                    except Exception as e:
                        logger.error(f"❌ Error in whale alert callback: {e}")

                whale_watcher.register_alert_callback(whale_alert_callback)
                logger.info("🔧 Enhanced Whale Watcher initialized with QuickNode streaming")

            except Exception as e:
                logger.warning(f"⚠️ Enhanced Whale Watcher initialization failed: {e}")
                whale_watcher = None

            # 🔧 PHASE 4: Initialize Strategy Selector for RANGING MARKET OPTIMIZATION
            strategy_selector = StrategySelector(
                config={'adaptive_weighting': {
                    'confidence_threshold': 0.05,             # 🔧 PHASE 4: 5% confidence threshold (VERY LOW FOR RANGING)
                    'min_strategy_weight': 0.1,               # 🔧 PHASE 4: 10% minimum weight (FIXED: was 1.0)
                    'max_strategy_weight': 1.0,               # 🔧 PHASE 4: 100% maximum weight
                    'performance_weight': 0.4,                # 40% performance influence
                    'regime_confidence_weight': 0.3,          # 30% regime influence
                    'risk_weight': 0.3                        # 30% risk influence
                }}
            )

            # 🔧 PHASE 4: Define available strategies with RANGING MARKET OPTIMIZATION
            available_strategies = {
                'opportunistic_volatility_breakout': {
                    'enabled': True,
                    'risk_level': 'medium',
                    'min_confidence': 0.05,  # 🔧 PHASE 4: LOWERED FOR RANGING MARKETS
                    'preferred_regimes': ['ranging', 'volatile', 'trending_up'],  # 🔧 PHASE 4: Added ranging
                    'regime_suitability': {
                        'trending_up': 0.9,
                        'trending_down': 0.3,
                        'ranging': 0.8,  # 🔧 PHASE 4: BOOSTED for ranging markets
                        'volatile': 0.8,
                        'choppy': 0.2,
                        'unknown': 0.6
                    }
                },
                'momentum_sol_usdc': {
                    'enabled': True,
                    'risk_level': 'medium',
                    'min_confidence': 0.05,  # 🔧 PHASE 4: LOWERED FOR RANGING MARKETS
                    'preferred_regimes': ['trending_up', 'ranging'],  # 🔧 PHASE 4: Added ranging
                    'regime_suitability': {
                        'trending_up': 0.95,
                        'trending_down': 0.4,
                        'ranging': 0.7,  # 🔧 PHASE 4: BOOSTED for ranging markets
                        'volatile': 0.6,
                        'choppy': 0.1,
                        'unknown': 0.5
                    }
                },
                'wallet_momentum': {
                    'enabled': True,
                    'risk_level': 'low',
                    'min_confidence': 0.01,  # 🔧 PHASE 4: VERY LOW FOR RANGING MARKETS
                    'preferred_regimes': ['ranging', 'trending_up', 'trending_down'],  # 🔧 PHASE 4: Ranging first
                    'regime_suitability': {
                        'trending_up': 0.8,
                        'trending_down': 0.6,
                        'ranging': 0.95,  # 🔧 PHASE 4: EXCELLENT for ranging markets
                        'volatile': 0.7,
                        'choppy': 0.3,
                        'unknown': 0.7
                    }
                }
            }

            # Register strategies with the selector
            for strategy_name, strategy_config in available_strategies.items():
                strategy_selector.register_strategy(strategy_name, strategy_config)
            logger.info("🎯 Initialized strategy selector with 3 available strategies")

            # Create config for position sizer with Phase 2 enhancements
            position_sizer_config = {
                'wallet': {
                    'active_trading_pct': 0.5,  # Use 50% of wallet as active capital
                    'reserve_pct': 0.5
                },
                'trading': {
                    'base_position_size_pct': 0.10,  # 🔧 REMOVED FILTER: Increased to 10% base position
                    'max_position_size_pct': 0.50,   # 🔧 REMOVED FILTER: Increased to 50% max (use half wallet)
                    'min_position_size_pct': 0.05,   # 🔧 REMOVED FILTER: Increased to 5% min (no tiny trades)
                    'min_trade_size_usd': 0,         # 🔧 REMOVED FILTER: No minimum trade size restriction
                    'target_trade_size_usd': 200,    # 🔧 REMOVED FILTER: Increased target to $200
                    'confidence_scaling': True,      # 🚀 PHASE 2: Enable confidence scaling
                    'regime_based_sizing': True      # 🚀 PHASE 2: Enable regime-based sizing
                },
                'risk_management': {
                    'max_risk_per_trade': 0.10,      # 🔧 REMOVED FILTER: Increased to 10% max risk per trade
                    'max_portfolio_exposure': 1.0,   # 🔧 REMOVED FILTER: Allow 100% portfolio exposure
                    'confidence_threshold': 0.01,    # 🔧 REMOVED FILTER: Minimal 1% confidence threshold
                    'regime_multipliers': {          # 🚀 PHASE 2: Market regime adjustments
                        'trending_up': 1.3,          # Boost in uptrends
                        'trending_down': 0.7,        # Reduce in downtrends
                        'ranging': 1.0,              # Normal in ranging (GOOD FOR CURRENT MARKET)
                        'volatile': 0.8,             # Reduce in volatile markets
                        'choppy': 0.4,               # Minimal in choppy markets
                        'unknown': 0.6               # Conservative when uncertain
                    }
                }
            }

            position_sizer = ProductionPositionSizer(position_sizer_config)
            position_sizer.update_wallet_state(
                wallet_balance=current_balance,
                current_exposure=0.0,  # Could track this dynamically
                sol_price=180.0  # Could be made dynamic
            )
            logger.info(f"💰 Initialized enhanced position sizer with {current_balance:.4f} SOL wallet balance")

            # 🔧 FIXED: Check if Birdeye is enabled before initializing
            try:
                # Load configuration to check if Birdeye is enabled
                from phase_4_deployment.core.unified_config import get_unified_config
                unified_config = get_unified_config()
                unified_config.load()
                birdeye_enabled = unified_config.get('apis.birdeye.enabled', False)

                if birdeye_enabled and self.birdeye_api_key:
                    logger.info("🔧 Using Birdeye scanner for opportunities")
                    scanner = BirdeyeScanner(self.birdeye_api_key)
                    opportunities = await scanner.scan_for_opportunities()
                    await scanner.close()
                else:
                    logger.info("🔧 Birdeye disabled - using fallback opportunity generation")
                    # Generate fallback opportunities without Birdeye
                    opportunities = await self._generate_fallback_opportunities()

            except Exception as e:
                logger.warning(f"⚠️ Error with opportunity scanning: {e}")
                logger.info("🔧 Using fallback opportunity generation")
                opportunities = await self._generate_fallback_opportunities()

            logger.info(f"📊 Found {len(opportunities)} opportunities")

            # 🚀 PHASE 3: Multi-Strategy Signal Generation with Adaptive Weighting
            signals = []

            # First, detect current market regime for timing filters
            try:
                # Create dummy price data for regime detection (in production, use real market data)
                import pandas as pd
                import numpy as np

                # Generate sample OHLCV data (replace with real market data in production)
                dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')
                sample_data = pd.DataFrame({
                    'timestamp': dates,
                    'open': np.random.normal(180, 5, 100),
                    'high': np.random.normal(182, 5, 100),
                    'low': np.random.normal(178, 5, 100),
                    'close': np.random.normal(180, 5, 100),
                    'volume': np.random.normal(1000000, 200000, 100)
                })

                # Detect market regime
                current_regime, regime_metrics, regime_probabilities = regime_detector.detect_regime(sample_data)
                regime_name = current_regime.value if current_regime else 'unknown'
                regime_confidence = max(regime_probabilities.values()) if regime_probabilities else 0.0
                logger.info(f"📊 MARKET REGIME: {regime_name} (confidence: {regime_confidence:.2f})")

            except Exception as e:
                logger.warning(f"⚠️ Could not detect market regime: {e}")
                current_regime = None
                regime_name = 'unknown'
                regime_confidence = 0.0
                regime_probabilities = {}

            # 🚀 PHASE 3: Load historical strategy performance for adaptive weighting
            try:
                # Create mock strategy performance data (in production, load from database)
                strategy_performance = {
                    'opportunistic_volatility_breakout': {
                        'total_trades': 15,
                        'net_pnl': 0.045,
                        'sharpe_ratio': 1.2,
                        'win_rate': 0.67,
                        'max_drawdown': -0.08,
                        'recent_pnl_7d': 0.012,
                        'volatility': 0.15
                    },
                    'momentum_sol_usdc': {
                        'total_trades': 12,
                        'net_pnl': 0.032,
                        'sharpe_ratio': 0.9,
                        'win_rate': 0.58,
                        'max_drawdown': -0.12,
                        'recent_pnl_7d': 0.008,
                        'volatility': 0.18
                    },
                    'wallet_momentum': {
                        'total_trades': 8,
                        'net_pnl': 0.021,
                        'sharpe_ratio': 0.7,
                        'win_rate': 0.75,
                        'max_drawdown': -0.05,
                        'recent_pnl_7d': 0.006,
                        'volatility': 0.12
                    }
                }

                # 🚀 PHASE 3: Update adaptive strategy weights based on performance
                updated_weights = adaptive_weight_manager.update_weights(
                    strategy_performance=strategy_performance,
                    market_regime=regime_name,
                    force_update=False
                )

                logger.info(f"🎯 ADAPTIVE WEIGHTS: {updated_weights}")

                # 🚀 PHASE 3: Select strategies based on regime and performance
                selected_strategies = strategy_selector.select_strategies(
                    market_regime=regime_name,
                    regime_confidence=regime_confidence,
                    strategy_weights=updated_weights,
                    strategy_performance=strategy_performance
                )

                # 🔧 SINGLE STRATEGY MODE: Force 100% allocation to best strategy
                if selected_strategies:
                    # Select only the best strategy (highest selection score)
                    best_strategy = max(selected_strategies, key=lambda s: s.get('selection_score', 0))
                    best_strategy['effective_allocation'] = 1.0  # Force 100% allocation
                    selected_strategies = [best_strategy]  # Use only the best strategy

                    logger.info(f"🎯 SINGLE STRATEGY MODE: {best_strategy['strategy_name']} selected with 100% allocation")
                    logger.info(f"  - Selection Score: {best_strategy.get('selection_score', 0):.3f}")
                    logger.info(f"  - Suitability Score: {best_strategy.get('suitability_score', 0):.3f}")
                else:
                    logger.info(f"📊 NO STRATEGIES SELECTED: {len(selected_strategies)} strategies chosen")

            except Exception as e:
                logger.warning(f"⚠️ Could not update adaptive weights: {e}")
                # 🔧 SINGLE STRATEGY MODE: Fallback to single strategy with 100% allocation
                selected_strategies = [{
                    'strategy_name': 'opportunistic_volatility_breakout',
                    'effective_allocation': 1.0,  # 🔧 SINGLE STRATEGY: 100% allocation
                    'suitability_score': 0.8,
                    'selection_score': 0.8
                }]
                updated_weights = {'opportunistic_volatility_breakout': 1.0}
                logger.info("🎯 SINGLE STRATEGY MODE: Using fallback strategy with 100% allocation")

            # 🚀 PHASE 3: Generate signals for each selected strategy with adaptive allocation
            for strategy_info in selected_strategies:
                strategy_name = strategy_info['strategy_name']
                strategy_allocation = strategy_info['effective_allocation']
                strategy_suitability = strategy_info['suitability_score']

                # Process opportunities for this strategy
                for opp in opportunities[:2]:  # Limit to top 2 per strategy
                    symbol = opp.get('symbol', 'UNKNOWN')

                    # 🔧 FIXED: Smart market pair mapping to prevent USDC-USDC invalid pairs
                    if symbol == 'USDC':
                        market_pair = 'SOL-USDC'  # Map USDC to SOL-USDC
                        logger.info(f"🔧 FIXED: Mapped USDC opportunity to SOL-USDC pair")
                    elif symbol == 'SOL':
                        market_pair = 'SOL-USDC'  # SOL to SOL-USDC
                    elif symbol in ['USDT', 'BONK', 'JUP', 'RAY', 'ORCA']:
                        market_pair = f"{symbol}-USDC"  # Valid pairs
                    else:
                        # For unknown tokens, default to SOL-USDC to avoid invalid pairs
                        market_pair = 'SOL-USDC'
                        logger.info(f"🔧 FIXED: Mapped unknown token {symbol} to SOL-USDC pair")

                    base_confidence = opp.get('score', 0.5)

                    # 🚀 PHASE 3: Adjust confidence based on strategy suitability
                    strategy_adjusted_confidence = base_confidence * strategy_suitability

                    # 🚀 PHASE 2: Create raw signal for enrichment
                    raw_signal = {
                        'action': 'BUY',
                        'market': market_pair,  # 🔧 FIXED: Use smart market pair mapping
                        'price': opp.get('price', 0),
                        'confidence': strategy_adjusted_confidence,
                        'timestamp': datetime.now().isoformat(),
                        'source': strategy_name,  # 🚀 PHASE 3: Use selected strategy name
                        'volume': opp.get('volume', 0),
                        'market_cap': opp.get('market_cap', 0),
                        'volatility': opp.get('volatility', 0.03),
                        'strategy_allocation': strategy_allocation,  # 🚀 PHASE 3: Include allocation
                        'strategy_suitability': strategy_suitability  # 🚀 PHASE 3: Include suitability
                    }

                # 🚀 PHASE 2: Enrich signal with confidence enhancement
                try:
                    enriched_signal = signal_enricher.enrich_signal(raw_signal)
                    enhanced_confidence = enriched_signal.get('confidence', base_confidence)
                    priority_score = enriched_signal.get('metadata', {}).get('priority_score', 0.5)

                    logger.info(f"🎯 SIGNAL ENRICHMENT: {base_confidence:.3f} → {enhanced_confidence:.3f} confidence (priority: {priority_score:.3f})")

                except Exception as e:
                    logger.warning(f"⚠️ Signal enrichment failed: {e}")
                    enriched_signal = raw_signal
                    enhanced_confidence = base_confidence
                    priority_score = 0.5

                # 🚀 PHASE 2: Apply confidence threshold filter
                confidence_threshold = position_sizer_config['risk_management']['confidence_threshold']
                if enhanced_confidence < confidence_threshold:
                    logger.info(f"❌ SIGNAL FILTERED: Confidence {enhanced_confidence:.3f} below threshold {confidence_threshold}")
                    continue

                # 🚀 PHASE 2: Apply market timing filter
                regime_name = current_regime.value if current_regime else 'unknown'
                regime_multiplier = position_sizer_config['risk_management']['regime_multipliers'].get(regime_name, 0.6)

                if regime_multiplier < 0.5:
                    logger.info(f"❌ MARKET TIMING FILTER: Regime '{regime_name}' unfavorable (multiplier: {regime_multiplier})")
                    continue

                # 🚀 PHASE 1 + 2 + 3: Dynamic Position Sizing with adaptive strategy allocation
                position_info = position_sizer.calculate_position_size(
                    signal_strength=enhanced_confidence,  # Use enhanced confidence
                    strategy=strategy_name,  # 🚀 PHASE 3: Use selected strategy
                    market_regime=regime_name,  # Use detected regime
                    volatility=enriched_signal.get('volatility', 0.03)
                )

                dynamic_size = position_info.get('position_size_sol', 0.01)

                # 🚀 PHASE 3: Apply strategy allocation multiplier
                strategy_allocated_size = dynamic_size * strategy_allocation

                # Apply regime multiplier to position size
                final_size = strategy_allocated_size * regime_multiplier

                logger.info(f"💰 PHASE 3 SIZING: {final_size:.4f} SOL")
                logger.info(f"📊 Strategy: {strategy_name} ({strategy_allocation:.1%} allocation)")
                logger.info(f"📊 Confidence: {base_confidence:.3f} → {enhanced_confidence:.3f}")
                logger.info(f"📊 Regime: {regime_name} (×{regime_multiplier})")
                logger.info(f"📊 Value: ${position_info.get('position_size_usd', 0) * strategy_allocation * regime_multiplier:.2f} USD")

                signal = {
                    'action': 'BUY',
                    'market': market_pair,  # 🔧 FIXED: Use smart market pair mapping (already defined above)
                    'price': opp.get('price', 0),
                    'size': final_size,  # 🎯 PHASE 3: Enhanced with strategy allocation + regime
                    'confidence': enhanced_confidence,
                    'timestamp': datetime.now().isoformat(),
                    'source': strategy_name,  # 🚀 PHASE 3: Use strategy name
                    'position_info': position_info,
                    'strategy_info': {  # 🚀 PHASE 3: Strategy allocation details
                        'strategy_name': strategy_name,
                        'allocation': strategy_allocation,
                        'suitability': strategy_suitability,
                        'base_size': dynamic_size,
                        'allocated_size': strategy_allocated_size,
                        'final_size': final_size
                    },
                    'regime_info': {
                        'regime': regime_name,
                        'multiplier': regime_multiplier,
                        'confidence': regime_confidence
                    },
                    'enrichment_info': {
                        'base_confidence': base_confidence,
                        'strategy_adjusted_confidence': strategy_adjusted_confidence,
                        'enhanced_confidence': enhanced_confidence,
                        'priority_score': priority_score
                    }
                }
                signals.append(signal)

            # Enrich signals
            if signals:
                enricher = SignalEnricher()
                enriched_signals = [enricher.enrich_signal(signal) for signal in signals]

                # Sort by priority score
                enriched_signals.sort(
                    key=lambda s: s.get('metadata', {}).get('priority_score', 0),
                    reverse=True
                )

                # Execute best signal
                best_signal = enriched_signals[0]
                logger.info(f"🎯 Selected best signal: {best_signal['market']} (score: {best_signal.get('metadata', {}).get('priority_score', 0)})")

                # Execute trade
                result = await self.execute_trade(best_signal)

                return {
                    'signals_generated': len(signals),
                    'signals_enriched': len(enriched_signals),
                    'trade_executed': result is not None,
                    'trade_result': result
                }
            else:
                logger.info("📭 No signals generated this cycle")
                return {
                    'signals_generated': 0,
                    'signals_enriched': 0,
                    'trade_executed': False,
                    'trade_result': None
                }

        except Exception as e:
            logger.error(f"❌ Error in trading cycle: {str(e)}")
            return {
                'error': str(e),
                'signals_generated': 0,
                'signals_enriched': 0,
                'trade_executed': False,
                'trade_result': None
            }

    async def _generate_fallback_opportunities(self):
        """🔧 NEW: Generate fallback opportunities when Birdeye is disabled."""
        logger.info("🔧 Generating fallback opportunities without Birdeye")

        # 🔧 PHASE 2: Generate opportunities using Enhanced Price Service
        try:
            from phase_4_deployment.utils.enhanced_price_service import get_enhanced_price_service
            price_service = await get_enhanced_price_service()

            # Get SOL price from enhanced price service (QuickNode -> Jupiter -> CoinGecko -> Fallback)
            sol_price_data = await price_service.get_token_price("So11111111111111111111111111111111111111112")
            sol_price = sol_price_data.get('value', 180.0) if sol_price_data else 180.0

            # Generate basic opportunities
            opportunities = [
                {
                    'symbol': 'SOL',
                    'price': sol_price,
                    'volume': 1000000,  # Mock volume
                    'market_cap': sol_price * 400000000,  # Approximate SOL market cap
                    'score': 0.7,  # Base confidence score
                    'volatility': 0.03,  # 3% volatility
                    'change_24h': 0.02  # 2% daily change
                },
                {
                    'symbol': 'USDC',
                    'price': 1.0,
                    'volume': 500000,
                    'market_cap': 1.0 * 25000000000,  # USDC market cap
                    'score': 0.6,
                    'volatility': 0.001,  # Very low volatility
                    'change_24h': 0.0
                }
            ]

            logger.info(f"🔧 Generated {len(opportunities)} fallback opportunities")
            return opportunities

        except Exception as e:
            logger.warning(f"⚠️ Error generating enhanced opportunities: {e}")
            # Return minimal fallback
            return [
                {
                    'symbol': 'SOL',
                    'price': 180.0,
                    'volume': 1000000,
                    'market_cap': 72000000000,
                    'score': 0.5,
                    'volatility': 0.03,
                    'change_24h': 0.0
                }
            ]

    async def run_fallback_trading_cycle(self):
        """Run a fallback trading cycle when advanced modules are not available."""
        logger.info("🔄 Running fallback trading cycle...")

        try:
            # Try to use dynamic position sizing even in fallback mode
            try:
                from core.risk.production_position_sizer import ProductionPositionSizer
                current_balance = await self.get_wallet_balance()

                # 🔧 REMOVED FILTER: Enhanced fallback config for full trading
                fallback_config = {
                    'wallet': {'active_trading_pct': 0.5, 'reserve_pct': 0.5},
                    'trading': {
                        'base_position_size_pct': 0.10,  # 🔧 REMOVED FILTER: Increased fallback base size
                        'max_position_size_pct': 0.50,   # 🔧 REMOVED FILTER: Increased fallback max size
                        'min_position_size_pct': 0.05,   # 🔧 REMOVED FILTER: Increased fallback min size
                        'min_trade_size_usd': 0,         # 🔧 REMOVED FILTER: No minimum trade size
                        'target_trade_size_usd': 100     # 🔧 REMOVED FILTER: Increased fallback target
                    },
                    'risk_management': {
                        'max_risk_per_trade': 0.05,      # 🔧 REMOVED FILTER: Increased fallback risk
                        'max_portfolio_exposure': 0.8    # 🔧 REMOVED FILTER: Increased fallback exposure
                    }
                }

                position_sizer = ProductionPositionSizer(fallback_config)
                position_sizer.update_wallet_state(
                    wallet_balance=current_balance,
                    current_exposure=0.0,
                    sol_price=180.0
                )

                position_info = position_sizer.calculate_position_size(
                    signal_strength=0.7,
                    strategy="fallback_generator",
                    market_regime="normal",
                    volatility=0.03
                )

                dynamic_size = position_info.get('position_size_sol', 0.001)
                logger.info(f"💰 FALLBACK ADAPTIVE SIZING: {dynamic_size:.4f} SOL")

            except Exception as e:
                logger.warning(f"⚠️ Could not use dynamic sizing in fallback: {e}")
                dynamic_size = 0.001  # Very small fallback size

            # Generate a simple test signal with dynamic sizing
            test_signal = {
                'action': 'BUY',
                'market': 'SOL-USDC',
                'price': 180.0,  # Approximate SOL price
                'size': dynamic_size,   # Now uses dynamic sizing when possible
                'confidence': 0.7,
                'timestamp': datetime.now().isoformat(),
                'source': 'fallback_generator'
            }

            logger.info(f"🎯 Generated fallback signal: {test_signal['market']}")

            # Execute the test signal if trading is enabled
            if self.trading_enabled and not self.dry_run:
                result = await self.execute_trade(test_signal)
                return {
                    'signals_generated': 1,
                    'signals_enriched': 1,
                    'trade_executed': result is not None,
                    'trade_result': result,
                    'mode': 'fallback'
                }
            else:
                logger.info("🧪 Fallback mode - dry run or trading disabled")
                return {
                    'signals_generated': 1,
                    'signals_enriched': 1,
                    'trade_executed': False,
                    'trade_result': {'success': True, 'signature': 'fallback_dry_run'},
                    'mode': 'fallback_dry_run'
                }

        except Exception as e:
            logger.error(f"❌ Error in fallback trading cycle: {str(e)}")
            return {
                'error': str(e),
                'signals_generated': 0,
                'signals_enriched': 0,
                'trade_executed': False,
                'trade_result': None,
                'mode': 'fallback_error'
            }

    async def run_live_trading(self, duration_minutes=30):
        """Run live trading for specified duration."""
        logger.info(f"🚀 Starting live trading session for {duration_minutes} minutes")

        # Initialize components
        if not await self.initialize_components():
            logger.error("❌ Failed to initialize components")
            return False

        # Check wallet balance
        if not await self.check_wallet_balance():
            logger.error("❌ Wallet balance check failed")
            return False

        # Print configuration
        logger.info("📋 Trading Configuration:")
        logger.info(f"   Wallet: {self.wallet_address}")
        logger.info(f"   Dry Run: {self.dry_run}")
        logger.info(f"   Paper Trading: {self.paper_trading}")
        logger.info(f"   Trading Enabled: {self.trading_enabled}")

        # Send session start notification
        if self.telegram_notifier and self.telegram_notifier.enabled:
            try:
                await self.telegram_notifier.notify_session_started(duration_minutes / 60.0)
                logger.info("📱 Session start notification sent")
            except Exception as e:
                logger.warning(f"⚠️ Failed to send session start notification: {e}")

        # Run trading cycles
        start_time = datetime.now()
        end_time = start_time.timestamp() + (duration_minutes * 60)
        cycle_count = 0

        try:
            while datetime.now().timestamp() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Starting cycle {cycle_count}")

                # Run trading cycle
                cycle_result = await self.run_trading_cycle()

                logger.info(f"📊 Cycle {cycle_count} results: {cycle_result}")

                # 🔧 ENHANCED: Send cycle completion alert
                signals_generated = cycle_result.get('signals_generated', 0)
                trades_executed = 1 if cycle_result.get('trade_executed', False) else 0
                await self._send_cycle_completion_alert(cycle_count, signals_generated, trades_executed)

                # Wait before next cycle
                await asyncio.sleep(60)  # 1 minute between cycles

        except KeyboardInterrupt:
            logger.info("⏹️ Trading stopped by user")
        except Exception as e:
            logger.error(f"❌ Error in trading session: {str(e)}")
        finally:
            # Send session end notification
            if self.telegram_notifier and self.telegram_notifier.enabled:
                try:
                    session_duration = (datetime.now() - start_time).total_seconds() / 60
                    metrics = {
                        'cycles_completed': cycle_count,
                        'trades_executed': 0,  # Would need to track this
                        'trades_rejected': 0,  # Would need to track this
                        'session_duration_minutes': session_duration
                    }
                    await self.telegram_notifier.notify_session_ended(metrics)
                    logger.info("📱 Session end notification sent")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to send session end notification: {e}")

            # Cleanup
            if self.executor:
                await self.executor.close()

            if self.telegram_notifier:
                await self.telegram_notifier.close()

            logger.info(f"🏁 Trading session completed. Ran {cycle_count} cycles.")

        return True

    async def _build_transaction_immediate(self, signal):
        """Build transaction with immediate blockhash handling to prevent timing issues."""
        try:
            logger.info("⚡ Building transaction with immediate blockhash handling...")

            # Use Orca transaction builder for reliable execution
            transaction = await self.build_orca_transaction(signal)
            if not transaction:
                logger.error("❌ Failed to build Orca transaction")
                return None

            logger.info("✅ Transaction built with immediate blockhash handling")
            return transaction

        except Exception as e:
            logger.error(f"❌ Error in immediate transaction building: {e}")
            return None

    async def _execute_transaction_immediate(self, transaction):
        """Execute transaction with immediate handling and proper error recovery."""
        try:
            logger.info("⚡ Executing transaction with immediate handling...")

            # Check transaction type and handle accordingly
            if isinstance(transaction, dict):
                execution_type = transaction.get('execution_type')

                if execution_type == 'orca_swap':
                    # Handle Orca transaction
                    if 'transaction' in transaction:
                        # Execute the raw transaction bytes
                        tx_bytes = transaction['transaction']

                        # FIXED: Use modern executor with immediate submission (signature verification fix)
                        if hasattr(self.executor, 'execute_transaction_with_bundles'):
                            logger.info("⚡ FIXED: Executing transaction with immediate submission to prevent signature verification failure")
                            result = await self.executor.execute_transaction_with_bundles(tx_bytes)
                        else:
                            logger.error("❌ FIXED: Modern executor required for signature verification fix")
                            return {'success': False, 'error': 'Modern executor required for immediate submission signature verification fix'}

                        if result and result.get('success', False):
                            # Enhance result with Orca-specific information
                            result.update({
                                'execution_type': 'orca_swap',
                                'input_token': transaction.get('input_token'),
                                'output_token': transaction.get('output_token'),
                                'input_amount': transaction.get('input_amount'),
                                'estimated_output': transaction.get('estimated_output'),
                                'min_output': transaction.get('min_output'),
                                'slippage_bps': transaction.get('slippage_bps')
                            })
                            logger.info("✅ Orca transaction executed successfully")
                            return result
                        else:
                            error = result.get('error', 'Unknown error') if result else 'No result returned'
                            logger.error(f"❌ Orca transaction failed: {error}")
                            return result
                    else:
                        logger.error("❌ Orca transaction missing transaction data")
                        return {'success': False, 'error': 'Missing transaction data'}

                elif execution_type == 'jupiter_swap':
                    # Handle Jupiter transaction
                    if 'transaction' in transaction:
                        # Jupiter returns base64 encoded transaction
                        import base64
                        tx_data = transaction['transaction']

                        # If it's a string, decode from base64
                        if isinstance(tx_data, str):
                            try:
                                tx_bytes = base64.b64decode(tx_data)
                            except Exception as e:
                                logger.error(f"❌ Failed to decode Jupiter transaction: {e}")
                                return {'success': False, 'error': f'Failed to decode transaction: {e}'}
                        else:
                            tx_bytes = tx_data

                        # 🔧 FIXED: Use modern executor with immediate submission and timing monitoring
                        if hasattr(self.executor, 'execute_transaction_with_bundles'):
                            logger.info("⚡ FIXED: Executing Jupiter transaction with immediate submission to prevent signature verification failure")

                            # 🔧 FIXED: Monitor submission timing for Jupiter transactions
                            import time
                            submission_start = time.time()

                            result = await self.executor.execute_transaction_with_bundles(tx_bytes)

                            submission_time = time.time() - submission_start
                            logger.info(f"⚡ Jupiter submission completed in {submission_time:.2f}s")

                            # 🔧 FIXED: Check if timing was within acceptable range
                            if submission_time > 2.0:
                                logger.warning(f"⚠️ TIMING WARNING: Jupiter submission took {submission_time:.2f}s (>2s may cause failures)")

                        else:
                            logger.error("❌ FIXED: Modern executor required for signature verification fix")
                            return {'success': False, 'error': 'Modern executor required for immediate submission signature verification fix'}

                        if result and result.get('success', False):
                            # Enhance result with Jupiter-specific information
                            result.update({
                                'execution_type': 'jupiter_swap',
                                'provider': 'jupiter',
                                'signal': transaction.get('signal', {})
                            })
                            logger.info("✅ Jupiter transaction executed successfully")

                            # 🔧 ENHANCED: Send trade execution alert
                            await self._send_trade_execution_alert(result)

                            return result
                        else:
                            error = result.get('error', 'Unknown error') if result else 'No result returned'
                            logger.error(f"❌ Jupiter transaction failed: {error}")
                            return result
                    else:
                        logger.error("❌ Jupiter transaction missing transaction data")
                        return {'success': False, 'error': 'Missing transaction data'}
            else:
                # Handle regular transaction
                # FIXED: Use modern executor with immediate submission (signature verification fix)
                if hasattr(self.executor, 'execute_transaction_with_bundles'):
                    logger.info("⚡ FIXED: Executing transaction with immediate submission to prevent signature verification failure")
                    result = await self.executor.execute_transaction_with_bundles(transaction)
                else:
                    logger.error("❌ FIXED: Modern executor required for signature verification fix")
                    return {'success': False, 'error': 'Modern executor required for immediate submission signature verification fix'}

                if result and result.get('success', False):
                    logger.info("✅ Transaction executed successfully with immediate handling")
                    return result
                else:
                    error = result.get('error', 'Unknown error') if result else 'No result returned'
                    logger.error(f"❌ Transaction failed with immediate handling: {error}")
                    return result

        except Exception as e:
            logger.error(f"❌ Error in immediate transaction execution: {e}")
            return {'success': False, 'error': str(e)}

    async def get_wallet_balance(self):
        """Get current wallet balance for validation."""
        try:
            # Use the existing balance checking method
            balance = await self.get_current_wallet_balance()
            if balance is not None:
                # Update the instance wallet balance
                self.wallet_balance = balance
                return balance
            else:
                return 0.0
        except Exception as e:
            logger.error(f"❌ Error getting wallet balance: {e}")
            return 0.0

    async def _send_trade_execution_alert(self, result):
        """🔧 ENHANCED: Send telegram alert for successful trade execution."""
        try:
            if not self.telegram_notifier:
                return

            # Extract trade information
            signal = result.get('signal', {})
            execution_type = result.get('execution_type', 'unknown')
            provider = result.get('provider', 'unknown')

            # Calculate trade value
            trade_size = signal.get('size', 0)
            market = signal.get('market', 'Unknown')
            action = signal.get('action', 'Unknown')

            # Create alert message
            alert_message = f"""
🎯 **TRADE EXECUTED SUCCESSFULLY**

📊 **Trade Details:**
• Market: {market}
• Action: {action}
• Size: {trade_size:.6f} SOL
• Provider: {provider.upper()}
• Type: {execution_type}

✅ **Execution Status:** SUCCESS
⚡ **Live Trading Active**

💰 **Current Session:**
• Wallet: {self.wallet_balance:.6f} SOL
• Trading Mode: LIVE (No Filters)
• Position Sizing: 50% Wallet Strategy

🚀 **System Status:** All systems operational
"""

            # Send to both primary and secondary chats
            await self.telegram_notifier.send_message(alert_message)
            logger.info("📱 Trade execution alert sent to Telegram")

        except Exception as e:
            logger.error(f"❌ Error sending trade execution alert: {e}")

    async def _send_trade_failure_alert(self, signal, error, execution_time=None):
        """🔧 ENHANCED: Send telegram alert for failed trade execution."""
        try:
            if not self.telegram_notifier:
                return

            # Extract trade information
            market = signal.get('market', 'Unknown')
            action = signal.get('action', 'Unknown')
            trade_size = signal.get('size', 0)

            # Create failure alert message
            alert_message = f"""
⚠️ **TRADE EXECUTION FAILED**

📊 **Trade Details:**
• Market: {market}
• Action: {action}
• Size: {trade_size:.6f} SOL

❌ **Error:** {error}
"""

            if execution_time:
                alert_message += f"⏱️ **Execution Time:** {execution_time:.2f}s\n"

            alert_message += f"""
🔧 **System Status:**
• Live Trading: Active
• Wallet: {self.wallet_balance:.6f} SOL
• Retry Logic: Enabled

🚀 **Next Action:** System will continue with next cycle
"""

            # Send alert
            await self.telegram_notifier.send_message(alert_message)
            logger.info("📱 Trade failure alert sent to Telegram")

        except Exception as e:
            logger.error(f"❌ Error sending trade failure alert: {e}")

    async def _send_cycle_completion_alert(self, cycle_number, signals_generated, trades_executed):
        """🔧 ENHANCED: Send telegram alert for cycle completion."""
        try:
            if not self.telegram_notifier:
                return

            # Only send alerts every 5 cycles to avoid spam
            if cycle_number % 5 != 0:
                return

            alert_message = f"""
🔄 **CYCLE {cycle_number} COMPLETED**

📊 **Cycle Summary:**
• Signals Generated: {signals_generated}
• Trades Executed: {trades_executed}
• Success Rate: {(trades_executed/signals_generated*100) if signals_generated > 0 else 0:.1f}%

💰 **Current Status:**
• Wallet Balance: {self.wallet_balance:.6f} SOL
• Trading Mode: LIVE (All Filters Removed)
• System Health: ✅ Operational

🚀 **Next Cycle:** Starting in 30 seconds
"""

            await self.telegram_notifier.send_message(alert_message)
            logger.info(f"📱 Cycle {cycle_number} completion alert sent")

        except Exception as e:
            logger.error(f"❌ Error sending cycle completion alert: {e}")


async def main():
    """Main function for unified live trading."""
    import argparse

    parser = argparse.ArgumentParser(description="Unified Live Trading System")
    parser.add_argument("--duration", type=float, default=30.0, help="Trading duration in minutes")
    parser.add_argument("--test-mode", action="store_true", help="Run in test mode with small trades")

    args = parser.parse_args()

    print("🚀 UNIFIED LIVE TRADING SYSTEM")
    print("="*60)
    print("⚠️  This system will execute REAL TRADES with REAL MONEY")
    print("="*60)

    # Create trader
    trader = UnifiedLiveTrader()

    # Run live trading
    success = await trader.run_live_trading(args.duration)

    if success:
        print("✅ Live trading session completed successfully")
        return 0
    else:
        print("❌ Live trading session failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
