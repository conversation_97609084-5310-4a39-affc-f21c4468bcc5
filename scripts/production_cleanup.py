#!/usr/bin/env python3
"""
Production System Cleanup - Remove Irrelevant Tests & Scripts
Removes outdated tests and scripts that are no longer relevant to the 
beautifully running, profitable production system.
"""

import os
import shutil
from pathlib import Path

# Files to remove - categorized by type
OUTDATED_TESTS = [
    "tests/test_carbon_core_integration.py",  # Carbon core not used in production
    "tests/test_tx_prep_service.py",  # Legacy transaction prep
    "tests/test_signature_verification_fix.py",  # Fixed and working
    "tests/test_wallet_security.py",  # Basic security, not production-specific
    "tests/test_momentum_optimizer.py",  # Strategy is optimized and working
    "tests/run_comprehensive_tests.py",  # Too broad, not focused
    "tests/run_tests.sh",  # Shell script not needed
]

DIAGNOSTIC_SCRIPTS = [
    "scripts/debug_transaction_execution.py",  # Transactions working perfectly
    "scripts/diagnose_signal_generation.py",  # Signals working perfectly
    "scripts/fix_jupiter_signature_issue.py",  # Fixed and working
    "scripts/quick_signature_test.py",  # Signatures working
    "scripts/test_signature_verification.py",  # Verification working
    "scripts/test_simple_jupiter_fix.py",  # Jupiter working
    "scripts/test_api_transaction_fixes.py",  # APIs working
    "scripts/test_fixed_trading_system.py",  # System is fixed and working
    "scripts/test_live_trading_rollback.py",  # No rollback needed
    "scripts/test_rollback.py",  # No rollback needed
    "scripts/test_rpc_endpoints.py",  # RPC working perfectly
    "scripts/verify_simulation.py",  # Not using simulation
    "scripts/verify_balance_change.py",  # Balance changes confirmed
]

REDUNDANT_ANALYSIS = [
    "scripts/analyze_trades.py",  # We have rich_trade_analyzer.py
    "scripts/check_live_trading_status.py",  # System status is clear
    "scripts/monitor_wallet_balance.py",  # Built into live trading
    "scripts/run_terminal_metrics.py",  # Dashboard handles metrics
    "scripts/sync_live_dashboard_metrics.py",  # Auto-synced
    "scripts/update_dashboard_for_production.py",  # Dashboard is production-ready
    "scripts/update_dashboard_real_balance.py",  # Real balance working
    "scripts/auto_sync_dashboard.py",  # Redundant sync
    "scripts/reset_dashboard_metrics.py",  # Not needed for production
    "scripts/reset_all_metrics.py",  # Not needed for production
]

LEGACY_SETUP = [
    "scripts/create_keypair_from_env.py",  # Keypair setup complete
    "scripts/direct_keypair_creation.py",  # Keypair working
    "scripts/generate_test_keypair.py",  # Test keypairs not needed
    "scripts/import_wallet.py",  # Wallet imported and working
    "scripts/validate_config.py",  # Config validated and working
    "scripts/test_unified_config.py",  # Config working
]

OPTIMIZATION_SCRIPTS = [
    "scripts/optimize_momentum.py",  # Momentum optimized (window=5, threshold=0.005)
    "scripts/restart_opportunistic_with_roi.py",  # ROI optimization complete
    "scripts/purge_mean_reversion.py",  # Mean reversion not used
]

EMERGENCY_TOOLS = [
    "scripts/emergency_position_flattener.py",  # Not needed for current system
]

# Keep these essential files
ESSENTIAL_FILES = [
    "scripts/unified_live_trading.py",  # MAIN ENTRY POINT
    "scripts/rich_trade_analyzer.py",  # Best analysis tool
    "scripts/analyze_live_metrics_profitability.py",  # Profitability analysis
    "scripts/comprehensive_system_test.py",  # System validation
    "scripts/final_production_verification.py",  # Production verification
    "scripts/system_status_check.py",  # Status monitoring
    "scripts/scheduled_profitability_telegram.py",  # Telegram alerts
    "scripts/send_profitability_telegram.py",  # Telegram alerts
    "scripts/quick_telegram_test.py",  # Telegram testing
    "scripts/generate_test_dashboard_data.py",  # Dashboard testing
    "scripts/generate_test_live_data.py",  # Live data testing
    "tests/test_full_system_integration.py",  # Full system test
    "tests/test_deployment_validation.py",  # Deployment validation
    "tests/test_transaction_execution_system.py",  # Transaction system test
    "tests/test_risk_management_system.py",  # Risk management test
    "tests/test_signal_generation_system.py",  # Signal generation test
    "tests/test_monitoring_and_health.py",  # Monitoring test
    "tests/run_tests.py",  # Main test runner
]

def cleanup_files():
    """Remove outdated and irrelevant files"""
    
    all_files_to_remove = (
        OUTDATED_TESTS + 
        DIAGNOSTIC_SCRIPTS + 
        REDUNDANT_ANALYSIS + 
        LEGACY_SETUP + 
        OPTIMIZATION_SCRIPTS + 
        EMERGENCY_TOOLS
    )
    
    removed_files = []
    kept_files = []
    
    print("🧹 PRODUCTION SYSTEM CLEANUP")
    print("=" * 50)
    print(f"📊 Analyzing {len(all_files_to_remove)} files for removal...")
    print()
    
    for file_path in all_files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"🗑️  REMOVED: {file_path}")
            except Exception as e:
                print(f"❌ ERROR removing {file_path}: {e}")
        else:
            print(f"⚠️  NOT FOUND: {file_path}")
    
    print()
    print("✅ ESSENTIAL FILES KEPT:")
    for essential_file in ESSENTIAL_FILES:
        if os.path.exists(essential_file):
            kept_files.append(essential_file)
            print(f"✅ KEPT: {essential_file}")
    
    print()
    print("📊 CLEANUP SUMMARY:")
    print(f"🗑️  Files Removed: {len(removed_files)}")
    print(f"✅ Essential Files Kept: {len(kept_files)}")
    print()
    
    # Update depr.txt
    update_depr_file(removed_files)
    
    return removed_files, kept_files

def update_depr_file(removed_files):
    """Update depr.txt with newly removed files"""
    
    if not removed_files:
        return
    
    depr_content = f"""
# === PRODUCTION CLEANUP - {len(removed_files)} FILES REMOVED ===
# Removed on production cleanup to shed weight from beautifully running system
# All files below were outdated/irrelevant to current profitable system

"""
    
    for file_path in removed_files:
        depr_content += f"# {file_path}\n"
    
    # Append to existing depr.txt
    with open("depr.txt", "a") as f:
        f.write(depr_content)
    
    print(f"📝 Updated depr.txt with {len(removed_files)} removed files")

if __name__ == "__main__":
    print("🚀 RWA Trading System - Production Cleanup")
    print("Removing irrelevant tests and scripts to shed weight")
    print()
    
    # Confirm cleanup
    response = input("🧹 Proceed with cleanup? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Cleanup cancelled")
        exit(0)
    
    removed, kept = cleanup_files()
    
    print()
    print("🎉 CLEANUP COMPLETE!")
    print("💰 System is now leaner and focused on profitable trading")
    print("🚀 Production system continues running beautifully")
